<?php

// Include configuration file
require_once 'config.php';
require_once 'JsonDatabase.php';

define('API_KEY', $API_KEY);

// Initialize JSON Database
$db = new JsonDatabase($JSON_DB_DIR);

/**
 * Function to find voucher by code in the old format (from callback.php)
 */
function findVoucherByCode($voucher_code) {
    $voucherFile = "data/vouchers/{$voucher_code}.json";
    if (file_exists($voucherFile)) {
        return json_decode(file_get_contents($voucherFile), true);
    }
    return null;
}

//-----------------------------------------------------------------------------------------
// Payment Gateway Functions (Zibal)

// Function to get voucher price (10,000 Toman per voucher + 7,000 commission)
function getVoucherPrice($count) {
    $voucherPrice = $count * 10000; // 10k per voucher
    $commission = 7000; // Fixed 7k commission
    return $voucherPrice + $commission;
}

// Function to convert Toman to Rial
function convertToRials($tomans) {
    return $tomans * 10;
}

// Function to generate payment link with Zibal gateway
function generatePaymentLink($user_id, $amount, $voucher_count) {
    global $db;

    // Zibal merchant key (replace with your actual key)
    $merchantKey = "zibal";

    // Create unique order ID - format: BV-TIMESTAMP-USERID
    $orderId = "BV-" . time() . "-" . $user_id;

    // Callback URL for payment return
    $callbackUrl = "https://speedx-team.ir/BitVoucher/callback.php";

    // Payment description
    $description = "خرید $voucher_count ووچر BitVoucher برای کاربر $user_id";

    // Payment parameters
    $parameters = array(
        "merchant" => $merchantKey,
        "callbackUrl" => $callbackUrl,
        "amount" => convertToRials($amount), // Convert to Rial
        "orderId" => $orderId,
        "description" => $description
    );

    // Save order information
    $orderData = [
        'user_id' => $user_id,
        'voucher_count' => $voucher_count,
        'amount' => $amount,
        'status' => 'pending',
        'created_at' => time()
    ];
    $db->save('orders', $orderId, $orderData);

    // Create logs directory if not exists
    if (!file_exists("logs")) {
        mkdir("logs", 0777, true);
    }

    // Log payment request
    file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " Payment request: user_id=$user_id, voucher_count=$voucher_count, amount=$amount, orderId=$orderId\n", FILE_APPEND);

    // Send request to Zibal and get response
    $result = postToZibal('request', $parameters);

    // Log response
    file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " Response: " . json_encode($result) . "\n", FILE_APPEND);

    // Check response
    if ($result === false) {
        file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: Failed to connect to Zibal gateway\n", FILE_APPEND);
        return false;
    }

    if (!isset($result->result)) {
        file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: Invalid response from Zibal\n", FILE_APPEND);
        return false;
    }

    // If request successful, return payment link
    if ($result->result == 100) {
        if (isset($result->trackId)) {
            return "https://gateway.zibal.ir/start/" . $result->trackId;
        } else {
            file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: trackId not found in response\n", FILE_APPEND);
            return false;
        }
    }

    // In case of error
    $errorCode = $result->result ?? 'unknown';
    $errorMessage = isset($result->message) ? $result->message : 'Unknown error';

    file_put_contents('logs/payment_request.log', date('Y-m-d H:i:s') . " ERROR: Zibal error code $errorCode: $errorMessage\n", FILE_APPEND);

    return false;
}

//-----------------------------------------------------------------------------------------

// Function to send request to Zibal API
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;

    // Create logs directory if not exists
    if (!file_exists("logs")) {
        mkdir("logs", 0777, true);
    }

    // Log request
    file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " REQUEST to $url: " . json_encode($parameters) . "\n", FILE_APPEND);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // Log response
    file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " RESPONSE (HTTP $httpCode): $result\n", FILE_APPEND);

    if ($curlError) {
        file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " CURL ERROR: $curlError\n", FILE_APPEND);
        return false;
    }

    if ($httpCode !== 200) {
        file_put_contents('logs/zibal_debug.log', date('Y-m-d H:i:s') . " HTTP ERROR: Code $httpCode\n", FILE_APPEND);
        return false;
    }

    return json_decode($result);
}

//-----------------------------------------------------------------------------------------

// Function to create استارز payment invoice
function createاستارزInvoice($user_id, $voucher_count, $استارز_amount) {
    global $db;

    // Create unique order ID for استارز payment
    $orderId = "استارز-" . time() . "-" . $user_id;

    // Save order information
    $orderData = [
        'user_id' => $user_id,
        'voucher_count' => $voucher_count,
        'استارز_amount' => $استارز_amount,
        'payment_method' => 'استارز',
        'status' => 'pending',
        'created_at' => time()
    ];
    $db->save('orders', $orderId, $orderData);

    // Create invoice parameters
    $invoice_params = [
        'chat_id' => $user_id,
        'title' => "BitVoucher Payment",
        'description' => "خرید $voucher_count عدد ووچر با پرداخت استارز، جهت پرداخت بر روی دکمه زیر کلیک کنید:",
        'payload' => $orderId,
        'currency' => 'XTR', // Telegram استارز currency code
        'prices' => json_encode([
            [
                'label' => "خرید $voucher_count ووچر",
                'amount' => $استارز_amount
            ]
        ])
    ];

    // Send invoice
    $result = bot('sendInvoice', $invoice_params);

    // Log the request
    if (!file_exists("logs")) {
        mkdir("logs", 0777, true);
    }
    file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " استارز invoice created: orderId=$orderId, user_id=$user_id, voucher_count=$voucher_count, استارز_amount=$استارز_amount\n", FILE_APPEND);

    return $result;
}

//-----------------------------------------------------------------------------------------

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null, $parse_mode = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    if ($parse_mode) {
        $data['parse_mode'] = $parse_mode;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function createMainMenuKeyboard()
{
    $keyboard = [
        [
            [
                'text' => '👀 مشاهده توکن',
                'callback_data' => 'view_tokens'
            ],
            [
                'text' => '💸 خرید و فروش',
                'callback_data' => 'buy_sell'
            ]
        ],
        [
            [
                'text' => '💳 کیف پول',
                'callback_data' => 'wallet'
            ],
            [
                'text' => '☎️ پشتیبانی',
                'callback_data' => 'support'
            ]
        ],
        [
            [
                'text' => '📚 راهنما و مستندات',
                'callback_data' => 'help'
            ],
            [
                'text' => '⚙️ تنظیمات کاربری',
                'callback_data' => 'settings'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

$update = json_decode(file_get_contents('php://input'));

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    // Save/update user data
    $userData = [
        'first_name' => $callback_query->from->first_name ?? '',
        'last_name' => $callback_query->from->last_name ?? '',
        'username' => $callback_query->from->username ?? '',
        'last_interaction' => time(),
        'step' => 'checking_membership'
    ];

    if (!$db->exists('users', $user_id)) {
        $db->saveUser($user_id, $userData);
    } else {
        // Check if existing user has a token, if not generate one
        $existingUser = $db->getUser($user_id);
        if (!isset($existingUser['token']) || empty($existingUser['token'])) {
            $userData['token'] = $db->generateUserToken($user_id);
        }
        $db->update('users', $user_id, $userData);
    }

    if ($data == 'check_membership') {
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            $first_name = $callback_query->from->first_name ?? 'کاربر';

            // Update user step to verified
            $db->updateUserStep($user_id, 'verified');

            // Check if user has pending voucher count from quick invoice
            $user = $db->getUser($user_id);
            $userData = $user['data'] ?? [];
            if (isset($userData['pending_voucher_count'])) {
                $voucher_count = $userData['pending_voucher_count'];
                unset($userData['pending_voucher_count']);
                $userData['voucher_count'] = $voucher_count;
                $db->updateUserData($user_id, $userData);

                // Show quick invoice after membership verification
                $totalPrice = $voucher_count * 10;
                $finalPrice = $totalPrice + 7;
                $استارزPrice = ceil($finalPrice * 1000 / 1318);

                $response_text = "⚡️ فاکتور آماده\n\n" .
                               "✱ خرید $voucher_count ووچر\n" .
                               "✱ کارمزد : 7هزارتومان\n" .
                               "✱ مبلغ کل : $finalPrice هزار تومان\n\n" .
                               "💳 فاکتور شما آماده است! روش پرداخت را انتخاب کنید:";

                // Create payment keyboard similar to lines 1995-2014
                $payment_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '⭐ پرداخت با ' . $استارزPrice . ' استارز',
                                'callback_data' => 'pay_with_استارز'
                            ],
                            [
                                'text' => '💳 درگاه پرداخت',
                                'callback_data' => 'pay_with_card'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                // First send thank you message
                $thank_you_text = "✨ از اینکه ما را برای پرداخت انتخاب کردید متشکریم!\n\n" .
                                "✱ سیستم در حال پردازش فاکتور شما می باشد کمی صبر کنید...";

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $thank_you_text
                ]);

                // Wait 4 seconds then edit to show invoice
                sleep(4);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $payment_keyboard
                ]);
                return;
            }

            $main_menu_text = "سلام $first_name 👋\n\n" .
                             "به ربات صرافی بیت ووچر خوش آمدید!\n\n" .
                             "با استفاده از این ربات شما می توانید پرداخت های تلگرامی خود را راحت تر انجام دهید.\n\n" .
                             "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";

            $main_menu_keyboard = createMainMenuKeyboard();

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => $main_menu_text,
                'parse_mode' => 'HTML',
                'reply_markup' => $main_menu_keyboard
            ]);
        } else {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ شما هنوز عضو نیستید!\n\n" .
                         "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
        }
    }

    // Handle main menu button callbacks
    elseif (in_array($data, ['buy_sell', 'view_tokens', 'support', 'wallet', 'settings', 'help', 'buy_voucher', 'sell_voucher', 'increase_voucher', 'decrease_voucher', 'price_info', 'voucher_count', 'payment', 'back_to_main', 'withdraw_balance', 'view_transactions', 'change_token', 'notifications', 'enable_notifications', 'disable_notifications', 'pay_with_استارز', 'pay_with_card', 'confirm_استارز_payment', 'transaction_history', 'guide', 'documentation', 'webservice_docs', 'bot_docs', 'view_api_output']) || strpos($data, 'view_transaction_') === 0 || strpos($data, 'transaction_history_page_') === 0) {
        $first_name = $callback_query->from->first_name ?? 'کاربر';

        switch ($data) {
            case 'buy_sell':
                $response_text = "💸 بخش خرید و فروش\n\nلطفا یکی از گزینه‌های زیر را انتخاب کنید:";

                // Create buy/sell keyboard with history button on top
                $buy_sell_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🗄 تاریخچه',
                                'callback_data' => 'transaction_history'
                            ]
                        ],
                        [
                            [
                                'text' => '✱ فروش ووچر',
                                'callback_data' => 'sell_voucher'
                            ],
                            [
                                'text' => '✱ خرید ووچر',
                                'callback_data' => 'buy_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $buy_sell_keyboard
                ]);
                exit;
                break;
            case 'view_tokens':
                // Get user data to retrieve token
                $user = $db->getUser($user_id);
                $userToken = $user['token'] ?? 'توکن یافت نشد';

                $response_text = "👀 توکن شما :\n" .
                               "<code>$userToken</code>\n\n" .
                               "✱ از قرار دادن توکن خود به سایر افراد جداً خودداری کنید.\n" .
                               "✱ این توکن دائمی می باشد و منقضی نخواهد شد.\n" .
                               "✱ جهت اطلاعات بیشتر بخش مستندات را بخوانید.";

                // Create back button keyboard for token view
                $token_back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'parse_mode' => 'HTML',
                    'reply_markup' => $token_back_keyboard
                ]);
                exit;
                break;
            case 'support':
                // Set user step to waiting for support message
                $db->updateUserStep($user_id, 'waiting_support_message');

                $response_text = "☎️ پشتیبانی\n\n" .
                               "لطفا پیام خود را برای تیم پشتیبانی ارسال کنید:\n\n" .
                               "✱ سوال، مشکل یا درخواست خود را به صورت کامل بنویسید\n" .
                               "✱ تیم پشتیبانی در اسرع وقت پاسخ شما را خواهد داد";

                // Create back button keyboard
                $support_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $support_keyboard
                ]);
                exit;
                break;
            case 'wallet':
                // Reset user step when entering wallet
                $db->updateUserStep($user_id, 'verified');

                // Get user balance using helper method
                $balance = $db->getUserBalance($user_id);

                $response_text = "💳 کیف پول\n\n" .
                               "✱ موجودی شما: " . number_format($balance) . " تومان\n\n" .
                               "✱ برای افزایش موجودی، ووچرهای خود را بفروشید.\n" .
                               "✱ موجودی از طریق فروش ووچرهایی که خودتان ساخته‌اید، افزایش می‌یابد.";

                // Create wallet keyboard with withdrawal and transactions options
                $wallet_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '📊 تراکنش‌ها',
                                'callback_data' => 'view_transactions'
                            ],
                            [
                                'text' => '💸 برداشت موجودی',
                                'callback_data' => 'withdraw_balance'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $wallet_keyboard
                ]);
                exit;
                break;
            case 'withdraw_balance':
                // Get user balance
                $balance = $db->getUserBalance($user_id);

                // Check minimum balance requirement (30,000 Toman)
                if ($balance < 30000) {
                    $response_text = "❌ برداشت موجودی\n\n" .
                                   "✱ موجودی شما: " . number_format($balance) . " تومان\n\n" .
                                   "✱ حداقل موجودی برای برداشت 30,000 تومان می‌باشد.\n" .
                                   "✱ برای افزایش موجودی، ووچرهای خود را بفروشید.";

                    $back_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت به کیف پول',
                                    'callback_data' => 'wallet'
                                ]
                            ]
                        ]
                    ]);

                    bot('editMessageText', [
                        'chat_id' => $chat_id,
                        'message_id' => $callback_query->message->message_id,
                        'text' => $response_text,
                        'reply_markup' => $back_keyboard
                    ]);
                    exit;
                } else {
                    // Set user step to waiting for card number
                    $db->updateUserStep($user_id, 'waiting_card_number');

                    $response_text = "💸 برداشت موجودی\n\n" .
                                   "✱ موجودی شما: " . number_format($balance) . " تومان\n\n" .
                                   "💳 لطفا شماره کارت 16 رقمی خود را به فرمت زیر ارسال کنید:\n\n" .
                                   "مثال: 1234-5678-9012-3456 : علی حسینی\n\n" .
                                   "✱ شماره کارت - فاصله - دونقطه - فاصله - نام صاحب کارت";

                    $back_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت به کیف پول',
                                    'callback_data' => 'wallet'
                                ]
                            ]
                        ]
                    ]);

                    bot('editMessageText', [
                        'chat_id' => $chat_id,
                        'message_id' => $callback_query->message->message_id,
                        'text' => $response_text,
                        'reply_markup' => $back_keyboard
                    ]);
                    exit;
                }
                break;
            case 'view_transactions':
                // Reset user step when viewing transactions
                $db->updateUserStep($user_id, 'verified');

                // Get user transactions (last 5)
                $allTransactions = $db->getUserTransactions($user_id);

                // Sort by created_at descending and get last 5
                usort($allTransactions, function($a, $b) {
                    return $b['created_at'] - $a['created_at'];
                });
                $recentTransactions = array_slice($allTransactions, 0, 5);

                if (empty($recentTransactions)) {
                    $response_text = "📊 تراکنش‌های اخیر\n\n" .
                                   "❌ هیچ تراکنشی یافت نشد!\n\n" .
                                   "✱ پس از انجام اولین تراکنش، تاریخچه شما در اینجا نمایش داده خواهد شد.";
                } else {
                    $response_text = "📊 تراکنش‌های اخیر\n\n";

                    foreach ($recentTransactions as $index => $transaction) {
                        $number = $index + 1;
                        $type = $transaction['type'];
                        $amount = number_format($transaction['amount']);
                        $date = date('Y/m/d H:i', $transaction['created_at']);
                        $status = $transaction['status'];

                        // Set transaction type in Persian
                        switch ($type) {
                            case 'voucher_sale':
                                $typeText = '🎟 فروش ووچر';
                                $statusIcon = '✅';
                                break;
                            case 'withdrawal':
                                $typeText = '💸 برداشت موجودی';
                                $statusIcon = ($status == 'completed') ? '✅' : '⏳';
                                break;
                            default:
                                $typeText = '📝 تراکنش';
                                $statusIcon = '✅';
                                break;
                        }

                        $response_text .= "$number. $typeText\n" .
                                        "   ✱ مبلغ: $amount تومان\n" .
                                        "   ✱ تاریخ: $date\n" .
                                        "   ✱ وضعیت: " . ($status == 'completed' ? 'تکمیل شده' : ($status == 'pending' ? 'در انتظار' : 'نامشخص')) . "\n\n";
                    }

                    $response_text .= "✱ تنها 5 تراکنش اخیر نمایش داده می‌شود.";
                }

                // Create back button keyboard
                $transactions_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به کیف پول',
                                'callback_data' => 'wallet'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $transactions_keyboard
                ]);
                exit;
                break;
            case 'settings':
                // Get user data for settings display
                $user = $db->getUser($user_id);
                $userToken = $user['token'] ?? 'توکن یافت نشد';
                $balance = $db->getUserBalance($user_id);
                $firstName = $user['first_name'] ?? 'نامشخص';
                $username = $user['username'] ?? 'نامشخص';

                $response_text = "⚙️ تنظیمات کاربری\n\n" .
                               "✱ نام شما : $firstName\n" .
                               "✱ آیدی عددی : $user_id\n" .
                               "✱ موجودی : " . number_format($balance) . " تومان\n" .
                               "✱ یوزرنیم : @$username\n\n" .
                               "جهت مدیریت بیشتر می توانید از دکمه های زیر استفاده کنید:";

                // Create settings keyboard
                $settings_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔔 اعلانات',
                                'callback_data' => 'notifications'
                            ],
                            [
                                'text' => '🔄 تغییر توکن',
                                'callback_data' => 'change_token'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $settings_keyboard
                ]);
                exit;
                break;
            case 'help':
                $response_text = "📚 راهنما و مستندات\n\nلطفا یکی از گزینه‌های زیر را انتخاب کنید:";

                // Create help menu keyboard
                $help_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '📘 مستندات',
                                'callback_data' => 'documentation'
                            ],
                            [
                                'text' => '📘 راهنما',
                                'callback_data' => 'guide'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $help_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'guide':
                $response_text = "📘 راهنمای استفاده از ربات\n\n" .
                               "🔹 <b>خرید ووچر:</b>\n" .
                               "✱ از منوی اصلی روی «خرید و فروش» کلیک کنید\n" .
                               "✱ گزینه «خرید ووچر» را انتخاب کنید\n" .
                               "✱ تعداد ووچر مورد نظر را تنظیم کنید\n" .
                               "✱ روش پرداخت (کارت یا استارز) را انتخاب کنید\n" .
                               "✱ پرداخت را تکمیل کنید\n\n" .
                               "🔹 <b>فروش ووچر:</b>\n" .
                               "✱ از منوی اصلی روی «خرید و فروش» کلیک کنید\n" .
                               "✱ گزینه «فروش ووچر» را انتخاب کنید\n" .
                               "✱ کد ووچر خود را ارسال کنید\n" .
                               "✱ مبلغ به کیف پول شما اضافه می‌شود\n\n" .
                               "🔹 <b>مشاهده تاریخچه:</b>\n" .
                               "✱ از بخش «خرید و فروش» روی «تاریخچه» کلیک کنید\n" .
                               "✱ تمام تراکنش‌های خرید و فروش خود را مشاهده کنید\n\n" .
                               "🔹 <b>کیف پول:</b>\n" .
                               "✱ موجودی خود را مشاهده کنید\n" .
                               "✱ درخواست برداشت وجه دهید\n" .
                               "✱ تراکنش‌های مالی را بررسی کنید\n\n" .
                               "❓ برای سوالات بیشتر با پشتیبانی تماس بگیرید.";

                // Create back button keyboard
                $guide_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به راهنما و مستندات',
                                'callback_data' => 'help'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $guide_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'documentation':
                $response_text = "📘 مستندات فنی\n\nلطفا یکی از گزینه‌های زیر را انتخاب کنید:";

                // Create documentation menu keyboard
                $documentation_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🌐 مستندات وبسرویس',
                                'callback_data' => 'webservice_docs'
                            ],
                            [
                                'text' => '🤖 مستندات ربات',
                                'callback_data' => 'bot_docs'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به راهنما و مستندات',
                                'callback_data' => 'help'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $documentation_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'webservice_docs':
                $response_text = "📚 مشاهده مستندات API\n\n" .
                               "کاربر گرامی جهت مشاهده مستندات به صورت کامل از دکمه زیر استفاده کنید.";

                // Create keyboard with mini app button
                $webservice_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '📚 مشاهده مستندات',
                                'web_app' => [
                                    'url' => 'https://speedx-team.ir/BitVoucher/api-docs.html'
                                ]
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به مستندات فنی',
                                'callback_data' => 'documentation'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $webservice_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'bot_docs':
                $response_text = "🤖 مستندات ربات\n\n" .
                               "💵 <b>قیمت‌گذاری:</b>\n" .
                               "✱ هر ووچر: 10,000 تومان\n" .
                               "✱ کارمزد ثابت: 7,000 تومان\n" .
                               "✱ مثال: خرید 2 ووچر = (2×10,000) + 7,000 = 27,000 تومان\n\n" .
                               "💳 <b>روش‌های پرداخت:</b>\n" .
                               "✱ درگاه بانکی\n" .
                               "✱ استارز تلگرام (1 استارز = 1,318 تومان)\n\n" .
                               "🎟 <b>فرمت کد ووچر:</b>\n" .
                               "✱ BV-XXXX-XXXX-XXXX-XXXX\n" .
                               "✱ هر کد ووچر منحصر به فرد است.\n" .
                               "✱ طول کد: 19 کاراکتر\n\n" .
                               "➖➖➖➖➖➖➖➖➖➖➖\n" .
                               "🤖 <b>مستندات پرداخت و فاکتور سازی سریع</b>\n" .
                               "✱ در این بخش می‌توانید نحوه پرداخت سریع (ساخت فاکتور) و ورود سریع به بات را ببینید.\n" .
                               "https://t.me/BitVoucherBot?start=1v\n" .
                               "✱ در لینک والا بالافاصله پس از ورود کاربر فاکتور خرید 1 بیت ووچر برای او نمایان و ایجاد می‌شود.\n" .
                               "✱ به جای عدد 1 می‌توانید تعداد ووچر مورد نظر را بگذارید.\n" .
                               "✱ تنها می‌توانید از اعداد 1 تا 50 استفاده کنید.\n\n" .
                               "➖➖➖➖➖➖➖➖➖➖➖\n\n" .
                               "✱ در صورت وجود مشکل، راهنمایی با پشتیانی تماس بگیرید.";

                // Create back button keyboard
                $bot_docs_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به مستندات فنی',
                                'callback_data' => 'documentation'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $bot_docs_keyboard,
                    'parse_mode' => 'HTML',
                    'disable_web_page_preview' => true
                ]);
                exit;
                break;
            case 'webservice_docs':
                $response_text = "📘 مستندات فنی\n\n" .
                               "✱ این بخش برای افرادی است که می خواهند از صرافی در ربات ها و یا سایر موارد استفاده داشته باشند.\n" .
                               "✱ لازم به ذکر است هرگونه خدمات غیرقانونی و یا غیراخلاقی هیچ گونه مسیولیت برای این صرافی نخواهد داشت.\n" .
                               "✱ برای مستندات باید از TOKEN استفاده کنید که در بخش \"👀 مشاهده توکن\" می باشد.\n" .
                               "➖➖➖➖➖➖➖➖➖➖\n" .
                               "✱ نحوه گرفتن اطلاعات کد ووچر : \n" .
                               "https://speedx-team.ir/BIT/api?token=TOKEN&code=CODE\n\n" .
                               "➖➖➖➖➖➖➖➖➖➖\n" .
                               "✱ نحوه انتقال کد ووچر به حساب شما : \n" .
                               "https://speedx-team.ir/BIT/api?token=TOKEN&bv-code=CODE&action=transmission\n\n" .
                               "➖➖➖➖➖➖➖➖➖➖\n" .
                               "✱ نحوه انتقال کد ووچر به کیف پول شما : \n" .
                               "https://speedx-team.ir/BIT/api?token=TOKEN&bv-code=CODE&action=wallet\n\n" .
                               "➖➖➖➖➖➖➖➖➖➖\n\n" .
                               "✱ در موارد بالا مقدار TOKEN همان توکن شما در بخش \"👀 مشاهده توکن\" است.\n" .
                               "✱ مقدار CODE همان کد ووچر است.\n" .
                               "✱ جهت مشاهده هر یک از خروجی ها بر روی دکمه زیر کلیک کنید:\n\n" .
                               "📖 جهت مشاهده مستندات کامل از لینک زیر استفاده کنید:";

                // Create keyboard with view output button and mini app
                $webservice_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '👁‍🗨 مشاهده خروجی',
                                'callback_data' => 'view_api_output'
                            ]
                        ],
                        [
                            [
                                'text' => '📖 مشاهده مستندات',
                                'web_app' => [
                                    'url' => 'https://speedx-team.ir/BitVoucher/api-docs.html'
                                ]
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به مستندات فنی',
                                'callback_data' => 'documentation'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $webservice_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'view_api_output':
                $response_text = "👁‍🗨 نمونه خروجی API\n\n" .
                               "<b>1️⃣ گرفتن اطلاعات کد ووچر:</b>\n" .
                               "<code>{\n" .
                               "  \"status\": \"success\",\n" .
                               "  \"voucher_code\": \"BV-1A7D-7EAE-F261-24DD\",\n" .
                               "  \"amount\": 20000,\n" .
                               "  \"currency\": \"IRR\",\n" .
                               "  \"is_used\": false,\n" .
                               "  \"created_at\": \"2025-07-28 19:01:26\"\n" .
                               "}</code>\n\n" .
                               "<b>2️⃣ انتقال کد ووچر به حساب:</b>\n" .
                               "<code>{\n" .
                               "  \"status\": \"success\",\n" .
                               "  \"message\": \"کد ووچر با موفقیت به حساب شما منتقل شد\",\n" .
                               "  \"amount\": 20000,\n" .
                               "  \"new_balance\": 45000\n" .
                               "}</code>\n\n" .
                               "<b>3️⃣ انتقال کد ووچر به کیف پول:</b>\n" .
                               "<code>{\n" .
                               "  \"status\": \"success\",\n" .
                               "  \"message\": \"کد ووچر با موفقیت به کیف پول اضافه شد\",\n" .
                               "  \"amount\": 20000,\n" .
                               "  \"wallet_balance\": 65000\n" .
                               "}</code>\n\n" .
                               "<b>❌ خطا در صورت کد نامعتبر:</b>\n" .
                               "<code>{\n" .
                               "  \"status\": \"error\",\n" .
                               "  \"message\": \"کد ووچر نامعتبر یا استفاده شده است\"\n" .
                               "}</code>";

                // Create back button keyboard
                $api_output_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به مستندات وبسرویس',
                                'callback_data' => 'webservice_docs'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $api_output_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'bot_docs':
                $response_text = "🤖 مستندات ربات\n\n" .
                               "🔸 <b>قیمت‌گذاری:</b>\n" .
                               "✱ هر ووچر: 10,000 تومان\n" .
                               "✱ کارمزد ثابت: 7,000 تومان\n" .
                               "✱ مثال: خرید 2 ووچر = (2×10,000) + 7,000 = 27,000 تومان\n\n" .
                               "🔸 <b>روش‌های پرداخت:</b>\n" .
                               "✱ درگاه بانکی (کارت به کارت)\n" .
                               "✱ استارز تلگرام (1 استارز = 1,318 تومان)\n\n" .
                               "🔸 <b>فرمت کد ووچر:</b>\n" .
                               "✱ BV-XXXX-XXXX-XXXX-XXXX\n" .
                               "✱ هر کد ووچر منحصر به فرد است\n" .
                               "✱ طول کد: 19 کاراکتر\n\n" .
                               "🔸 <b>امنیت ربات:</b>\n" .
                               "✱ تمام تراکنش‌ها رمزنگاری شده‌اند\n" .
                               "✱ کدهای ووچر در دیتابیس امن ذخیره می‌شوند\n" .
                               "✱ هر کد ووچر فقط یک بار قابل استفاده است\n\n" .
                               "🔸 <b>پشتیبانی:</b>\n" .
                               "✱ پاسخگویی 24 ساعته\n" .
                               "✱ حل مشکلات فنی\n" .
                               "✱ راهنمایی در استفاده از ربات\n\n" .
                               "🔸 <b>محدودیت‌ها:</b>\n" .
                               "✱ حداکثر 10 ووچر در هر خرید\n" .
                               "✱ حداقل مبلغ برداشت: 10,000 تومان\n" .
                               "✱ حداکثر موجودی کیف پول: 10,000,000 تومان";

                // Create back button keyboard
                $bot_docs_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به مستندات فنی',
                                'callback_data' => 'documentation'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $bot_docs_keyboard,
                    'parse_mode' => 'HTML'
                ]);
                exit;
                break;
            case 'buy_voucher':
                // Reset voucher count to 1 when entering buy voucher menu
                $user = $db->getUser($user_id);
                $userData = $user['data'] ?? [];
                $userData['voucher_count'] = 1;
                $db->updateUserData($user_id, $userData);

                $response_text = "💳 خرید ووچر\n\nلطفا مقدار ووچر که میخواهید خریداری کنید را تعیین کنید:";

                // Create voucher purchase keyboard
                $voucher_buy_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '✱ قیمت : 10هزارتومان ✱',
                                'callback_data' => 'price_info'
                            ]
                        ],
                        [
                            [
                                'text' => '➖',
                                'callback_data' => 'decrease_voucher'
                            ],
                            [
                                'text' => '1 ووچر',
                                'callback_data' => 'voucher_count'
                            ],
                            [
                                'text' => '➕',
                                'callback_data' => 'increase_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '✨ پرداخت',
                                'callback_data' => 'payment'
                            ],
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_sell'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $voucher_buy_keyboard
                ]);
                exit;
                break;
            case 'sell_voucher':
                // Set user step to waiting for voucher code
                $db->updateUserStep($user_id, 'waiting_voucher_code');

                $response_text = "✱ فروش ووچر\n\n" .
                               "✱ لطفا کد ووچری که می‌خواهید بفروشید را ارسال کنید:\n\n" .
                               "✱ مثال: BV-XXXX-XXXX-XXXX-XXXX";

                // Create back button keyboard
                $back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $back_keyboard
                ]);
                exit;
                break;
            case 'increase_voucher':
                // Get current voucher count from user data or default to 1
                $user = $db->getUser($user_id);
                $currentCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;

                // Check if already at maximum
                if ($currentCount >= 50) {
                    // Show alert for maximum limit reached
                    bot('answerCallbackQuery', [
                        'callback_query_id' => $callback_query->id,
                        'text' => '❌ حداکثر 50 ووچر قابل انتخاب است!',
                        'show_alert' => false
                    ]);
                    exit;
                }

                $newCount = $currentCount + 1;

                // Update user data
                $userData = $user['data'] ?? [];
                $userData['voucher_count'] = $newCount;
                $db->updateUserData($user_id, $userData);

                // Update the message with new count
                $response_text = "💳 خرید ووچر\n\nلطفا مقدار ووچر که میخواهید خریداری کنید را تعیین کنید:";
                $totalPrice = $newCount * 10;

                $voucher_buy_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => "✱ قیمت : {$totalPrice}هزارتومان ✱",
                                'callback_data' => 'price_info'
                            ]
                        ],
                        [
                            [
                                'text' => '➖',
                                'callback_data' => 'decrease_voucher'
                            ],
                            [
                                'text' => "$newCount ووچر",
                                'callback_data' => 'voucher_count'
                            ],
                            [
                                'text' => '➕',
                                'callback_data' => 'increase_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '✨ پرداخت',
                                'callback_data' => 'payment'
                            ],
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_sell'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $voucher_buy_keyboard
                ]);
                exit;
                break;
            case 'decrease_voucher':
                // Get current voucher count from user data or default to 1
                $user = $db->getUser($user_id);
                $currentCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;
                $newCount = max($currentCount - 1, 1); // Min 1 voucher

                // Update user data
                $userData = $user['data'] ?? [];
                $userData['voucher_count'] = $newCount;
                $db->updateUserData($user_id, $userData);

                // Update the message with new count
                $response_text = "💳 خرید ووچر\n\nلطفا مقدار ووچر که میخواهید خریداری کنید را تعیین کنید:";
                $totalPrice = $newCount * 10;

                $voucher_buy_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => "✱ قیمت : {$totalPrice}هزارتومان ✱",
                                'callback_data' => 'price_info'
                            ]
                        ],
                        [
                            [
                                'text' => '➖',
                                'callback_data' => 'decrease_voucher'
                            ],
                            [
                                'text' => "$newCount ووچر",
                                'callback_data' => 'voucher_count'
                            ],
                            [
                                'text' => '➕',
                                'callback_data' => 'increase_voucher'
                            ]
                        ],
                        [
                            [
                                'text' => '✨ پرداخت',
                                'callback_data' => 'payment'
                            ],
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_sell'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $voucher_buy_keyboard
                ]);
                exit;
                break;
            case 'price_info':
            case 'voucher_count':
                // Do nothing, just ignore these clicks
                exit;
                break;
            case 'payment':
                // Get user's voucher count and calculate total
                $user = $db->getUser($user_id);
                $voucherCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;

                // Calculate prices using the payment function
                $totalPrice = getVoucherPrice($voucherCount);

                // Calculate استارز price (1 Star = 1,318 Toman based on current rate)
                $استارزPrice = ceil($totalPrice / 1318);

                $response_text = "✨ پرداخت ووچر\n\n" .
                               "✱ تعداد ووچر: $voucherCount عدد\n" .
                               "✱ کارمزد : 7هزارتومان\n" .
                               "✱ مبلغ کل : " . number_format($totalPrice) . " تومان\n\n" .
                               "لطفا روش پرداخت خود را انتخاب کنید:";

                // Create payment method selection keyboard
                $payment_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '⭐ پرداخت با ' . $استارزPrice . ' استارز',
                                'callback_data' => 'pay_with_استارز'
                            ],
                            [
                                'text' => '💳 درگاه پرداخت',
                                'callback_data' => 'pay_with_card'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'buy_voucher'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $payment_keyboard
                ]);
                exit;
                break;
            case 'back_to_main':
                // Reset user step and show main menu
                $db->updateUserStep($user_id, 'verified');

                $first_name = $callback_query->from->first_name ?? 'کاربر';
                $main_menu_text = "سلام $first_name 👋\n\n" .
                                 "به ربات صرافی بیت ووچر خوش آمدید!\n\n" .
                                 "با استفاده از این ربات شما می توانید پرداخت های تلگرامی خود را راحت تر انجام دهید.\n\n" .
                                 "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";

                $main_menu_keyboard = createMainMenuKeyboard();

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $main_menu_text,
                    'parse_mode' => 'HTML',
                    'reply_markup' => $main_menu_keyboard
                ]);
                exit;
                break;
            case 'change_token':
                // Generate new token for user
                $user = $db->getUser($user_id);
                $newToken = $db->generateUserToken($user_id);

                // Update user with new token
                $db->update('users', $user_id, ['token' => $newToken]);

                $response_text = "🔄 تغییر توکن\n\n" .
                               "✱ توکن جدید با موفقیت تولید شد!\n\n" .
                               "🔑 توکن جدید شما:\n" .
                               "<code>$newToken</code>\n\n" .
                               "✱ توکن قبلی شما دیگر قابل استفاده نیست.\n" .
                               "✱ از قرار دادن توکن خود به سایر افراد جداً خودداری کنید.";

                // Create back button keyboard
                $token_back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به تنظیمات',
                                'callback_data' => 'settings'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'parse_mode' => 'HTML',
                    'reply_markup' => $token_back_keyboard
                ]);
                exit;
                break;
            case 'notifications':
                // Get user notification settings (default to enabled)
                $user = $db->getUser($user_id);
                $userData = $user['data'] ?? [];
                $notificationsEnabled = isset($userData['notifications_enabled']) ? $userData['notifications_enabled'] : true;

                $statusText = $notificationsEnabled ? "✅ فعال" : "❌ غیرفعال";
                $toggleText = $notificationsEnabled ? "غیرفعال کردن" : "فعال کردن";
                $toggleCallback = $notificationsEnabled ? "disable_notifications" : "enable_notifications";

                $response_text = "🔔 تنظیمات اعلانات\n\n" .
                               "✱ وضعیت فعلی: $statusText\n\n" .
                               "✱ اعلانات شامل موارد ارسالی و غیرضروری سیستم اطلاع رسانی است.\n\n" .
                               "برای تغییر وضعیت اعلانات از دکمه زیر استفاده کنید:";

                // Create notifications keyboard
                $notifications_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => "🔔 $toggleText اعلانات",
                                'callback_data' => $toggleCallback
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به تنظیمات',
                                'callback_data' => 'settings'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $notifications_keyboard
                ]);
                exit;
                break;
            case 'enable_notifications':
                // Enable notifications for user
                $user = $db->getUser($user_id);
                $userData = $user['data'] ?? [];
                $userData['notifications_enabled'] = true;
                $db->updateUserData($user_id, $userData);

                $response_text = "🔔 تنظیمات اعلانات\n\n" .
                               "✅ اعلانات با موفقیت فعال شد!\n\n" .
                               "✱ از این پس اعلانات غیر ضروری هم برای شما ارسال خواهد شد.";

                // Create back button keyboard
                $notification_back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به تنظیمات',
                                'callback_data' => 'settings'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $notification_back_keyboard
                ]);
                exit;
                break;
            case 'disable_notifications':
                // Disable notifications for user
                $user = $db->getUser($user_id);
                $userData = $user['data'] ?? [];
                $userData['notifications_enabled'] = false;
                $db->updateUserData($user_id, $userData);

                $response_text = "🔔 تنظیمات اعلانات\n\n" .
                               "✱ اعلانات با موفقیت غیرفعال شد!\n" .
                               "✱ از این پس اعلانات برای شما ارسال نخواهد شد.\n" .
                               "✱ توجه: پیام‌های مهم و ضروری سیستم همچنان ارسال خواهد شد.\n\n" .
                               "✨ می‌توانید هر زمان که بخواهید اعلانات را مجدداً فعال کنید.";

                // Create back button keyboard
                $notification_back_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به تنظیمات',
                                'callback_data' => 'settings'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $notification_back_keyboard
                ]);
                exit;
                break;
            case 'pay_with_card':
                // Get user's voucher count and calculate total
                $user = $db->getUser($user_id);
                $voucherCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;

                // Calculate prices using the payment function
                $totalPrice = getVoucherPrice($voucherCount);

                // Generate payment link
                $paymentLink = generatePaymentLink($user_id, $totalPrice, $voucherCount);

                if ($paymentLink) {
                    // Payment link generated successfully
                    $response_text = "💳 درگاه پرداخت بانکی\n\n" .
                                   "✱ تعداد ووچر: $voucherCount عدد\n" .
                                   "✱ کارمزد : 7هزارتومان\n" .
                                   "✱ مبلغ کل : " . number_format($totalPrice) . " تومان\n\n" .
                                   "<blockquote>⚠️ جهت پرداخت لطفا اتصال VPN خود را قطع کنید.</blockquote>";

                    // Create payment and back buttons
                    $payment_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '💳 پرداخت',
                                    'url' => $paymentLink
                                ]
                            ],
                            [
                                [
                                    'text' => '🔙 بازگشت',
                                    'callback_data' => 'payment'
                                ]
                            ]
                        ]
                    ]);
                } else {
                    // Payment link generation failed
                    $response_text = "❌ خطا در ایجاد لینک پرداخت\n\n" .
                                   "متأسفانه در حال حاضر امکان پرداخت وجود ندارد.\n" .
                                   "لطفاً بعداً تلاش کنید.\n\n" .
                                   "✱ تعداد ووچر: $voucherCount عدد\n" .
                                   "✱ مبلغ کل : " . number_format($totalPrice) . " تومان";

                    // Create only back button
                    $payment_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت',
                                    'callback_data' => 'payment'
                                ]
                            ]
                        ]
                    ]);
                }

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'parse_mode' => 'HTML',
                    'reply_markup' => $payment_keyboard
                ]);
                exit;
                break;
            case 'pay_with_استارز':
                // Get user's voucher count and calculate total
                $user = $db->getUser($user_id);
                $voucherCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;

                // Calculate prices
                $totalPrice = getVoucherPrice($voucherCount);
                $استارزPrice = ceil($totalPrice / 1318);

                $response_text = "⭐ پرداخت با Telegram استارز\n\n" .
                               "✱ تعداد ووچر: $voucherCount عدد\n" .
                               "✱ کارمزد : 7هزارتومان\n" .
                               "✱ مبلغ کل : " . number_format($totalPrice) . " تومان\n" .
                               "✱ قیمت استارز: $استارزPrice ⭐\n\n" .
                               "✱ پرداخت با استارز سریع و آسان است!\n" .
                               "✱ پرداخت کاملاً امن و فوری انجام می‌شود.";

                // Create استارز payment keyboard
                $استارز_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => "⭐ پرداخت $استارزPrice استارز",
                                'callback_data' => 'confirm_استارز_payment'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'payment'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $استارز_keyboard
                ]);
                exit;
                break;
        }

        // Handle pagination callbacks for transaction history
        if (strpos($data, 'transaction_history_page_') === 0 || $data == 'transaction_history') {
            // Get page number from callback data (default to page 1)
            $page = 1;
            if (strpos($data, 'transaction_history_page_') === 0) {
                $page = intval(substr($data, 25)); // Remove 'transaction_history_page_' prefix
            }

                // Get user's transaction history (all transactions)
                $allTransactions = $db->getUserTransactions($user_id);

                // Also get orders (purchases) from orders table
                $allOrders = $db->getAll('orders');
                $userOrders = [];
                foreach ($allOrders as $orderId => $order) {
                    if ($order['user_id'] == $user_id && $order['status'] == 'completed') {
                        $userOrders[] = [
                            'id' => $orderId, // Use the order ID as the key
                            'type' => 'voucher_purchase',
                            'amount' => isset($order['amount']) ? $order['amount'] - 7000 : 0, // Subtract commission
                            'created_at' => $order['completed_at'] ?? $order['created_at'],
                            'payment_method' => isset($order['payment_method']) ? $order['payment_method'] : 'card',
                            'voucher_count' => $order['voucher_count'] ?? 1,
                            'voucher_code' => $order['voucher_code'] ?? null
                        ];
                    }
                }

                // Merge transactions and orders
                $allUserTransactions = array_merge($allTransactions, $userOrders);

                // Filter only voucher purchase and sale transactions
                $filteredTransactions = [];
                foreach ($allUserTransactions as $transaction) {
                    if (in_array($transaction['type'], ['voucher_purchase', 'voucher_sale'])) {
                        $filteredTransactions[] = $transaction;
                    }
                }

                // Sort by created_at descending (newest first)
                usort($filteredTransactions, function($a, $b) {
                    return $b['created_at'] - $a['created_at'];
                });

                // Pagination settings
                $transactionsPerPage = 10;
                $totalTransactions = count($filteredTransactions);
                $totalPages = ceil($totalTransactions / $transactionsPerPage);
                $offset = ($page - 1) * $transactionsPerPage;
                $pageTransactions = array_slice($filteredTransactions, $offset, $transactionsPerPage);

                if (empty($filteredTransactions)) {
                    $response_text = "🗄 تاریخچه تراکنش‌ها\n\n" .
                                   "❌ هیچ تراکنشی یافت نشد!\n\n" .
                                   "✱ پس از خرید یا فروش اولین ووچر، تاریخچه شما در اینجا نمایش داده خواهد شد.";

                    // Create back button keyboard
                    $history_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت به خرید و فروش',
                                    'callback_data' => 'buy_sell'
                                ]
                            ]
                        ]
                    ]);
                } else {
                    $response_text = "🗄 تاریخچه تراکنش‌ها\n\n" .
                                   "✱ برای مشاهده جزئیات هر تراکنش، روی آن کلیک کنید:";

                    // Create transaction buttons
                    $transaction_buttons = [];
                    foreach ($pageTransactions as $index => $transaction) {
                        // Calculate the actual transaction number (from newest to oldest)
                        $actualIndex = $offset + $index;
                        $transactionNumber = $totalTransactions - $actualIndex;
                        $type = $transaction['type'];

                        // Set transaction type in Persian
                        switch ($type) {
                            case 'voucher_purchase':
                                $typeText = '💳 تراکنش خرید';
                                break;
                            case 'voucher_sale':
                                $typeText = '💳 تراکنش فروش';
                                break;
                            default:
                                $typeText = '💳 تراکنش';
                                break;
                        }

                        // Create unique callback data for each transaction
                        $transactionId = $transaction['id'] ?? 'txn_' . $transaction['created_at'] . '_' . $actualIndex . '_' . $user_id;

                        $transaction_buttons[] = [
                            [
                                'text' => "$typeText #$transactionNumber",
                                'callback_data' => 'view_transaction_' . base64_encode($transactionId)
                            ]
                        ];

                        // Store transaction data temporarily for retrieval
                        $tempTransactionData = [
                            'transaction' => $transaction,
                            'index' => $transactionNumber
                        ];
                        $db->save('temp_transactions', $transactionId, $tempTransactionData);
                    }

                    // Add pagination buttons if needed
                    if ($totalPages > 1) {
                        $pagination_buttons = [];

                        // Previous page button
                        if ($page > 1) {
                            $pagination_buttons[] = [
                                'text' => '➡️ قبلی',
                                'callback_data' => 'transaction_history_page_' . ($page - 1)
                            ];
                        }

                        // Next page button
                        if ($page < $totalPages) {
                            $pagination_buttons[] = [
                                'text' => 'بعدی ⬅️',
                                'callback_data' => 'transaction_history_page_' . ($page + 1)
                            ];
                        }

                        // Page info button on the right
                        $pagination_buttons[] = [
                            'text' => "📄 صفحه $page از $totalPages",
                            'callback_data' => 'page_info' // Non-functional button
                        ];

                        $transaction_buttons[] = $pagination_buttons;
                    }

                    // Add back button
                    $transaction_buttons[] = [
                        [
                            'text' => '🔙 بازگشت به خرید و فروش',
                            'callback_data' => 'buy_sell'
                        ]
                    ];

                    $history_keyboard = json_encode(['inline_keyboard' => $transaction_buttons]);
                }

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $history_keyboard
                ]);
                exit;
        }

        // Handle individual transaction view
        if (strpos($data, 'view_transaction_') === 0) {
            $transactionId = base64_decode(substr($data, 17)); // Remove 'view_transaction_' prefix
            $tempTransactionData = $db->load('temp_transactions', $transactionId);

            if ($tempTransactionData) {
                $transaction = $tempTransactionData['transaction'];
                $number = $tempTransactionData['index'];
                $type = $transaction['type'];
                $amount = number_format($transaction['amount']);
                $date = date('Y/m/d H:i:s', $transaction['created_at']);

                // Create detailed transaction info
                if ($type == 'voucher_purchase') {
                    $typeText = '🛒 خرید ووچر';
                    $paymentMethod = isset($transaction['payment_method']) ? $transaction['payment_method'] : 'کارت';
                    $paymentMethodText = ($paymentMethod == 'استارز') ? 'استارز تلگرام' : 'درگاه بانکی';

                    $response_text = "💳 جزئیات تراکنش خرید #$number\n\n" .
                                   "✱ نوع: $typeText\n" .
                                   "✱ ارزش ووچر: $amount تومان\n" .
                                   "✱ تاریخ: $date\n" .
                                   "✱ روش پرداخت: $paymentMethodText\n" .
                                   "✱ وضعیت: تکمیل شده\n\n";

                    // Add voucher code if available
                    if (isset($transaction['voucher_code']) && $transaction['voucher_code']) {
                        $response_text .= "🎟 کد ووچر: <code>" . $transaction['voucher_code'] . "</code>\n\n";
                    }

                    $response_text .= "✱ این ووچر با موفقیت خریداری شده است.";

                } else if ($type == 'voucher_sale') {
                    $typeText = '🎟 فروش ووچر';

                    $response_text = "💳 جزئیات تراکنش فروش #$number\n\n" .
                                   "✱  نوع: $typeText\n" .
                                   "✱  مبلغ: $amount تومان\n" .
                                   "✱  تاریخ: $date\n" .
                                   "✱  وضعیت: تکمیل شده\n\n";

                    // Add voucher code if available
                    if (isset($transaction['voucher_code']) && $transaction['voucher_code']) {
                        $response_text .= "🎟 کد ووچر فروخته شده: <code>" . $transaction['voucher_code'] . "</code>\n\n";
                    }

                    $response_text .= "✱ این ووچر با موفقیت فروخته شده و مبلغ به کیف پول شما اضافه شده است.";
                }

                // Create back button
                $transaction_detail_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '🔙 بازگشت به تاریخچه',
                                'callback_data' => 'transaction_history'
                            ]
                        ]
                    ]
                ]);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $transaction_detail_keyboard,
                    'parse_mode' => 'HTML'
                ]);

                // Clean up temporary data
                $db->delete('temp_transactions', $transactionId);

            } else {
                // Transaction not found, go back to history
                bot('answerCallbackQuery', [
                    'callback_query_id' => $callback_query->id,
                    'text' => '❌ اطلاعات تراکنش یافت نشد!',
                    'show_alert' => true
                ]);
            }
            exit;
        }

        switch ($data) {
            case 'confirm_استارز_payment':
                // Get user's voucher count and calculate total
                $user = $db->getUser($user_id);
                $voucherCount = isset($user['data']['voucher_count']) ? $user['data']['voucher_count'] : 1;

                // Calculate استارز price
                $totalPrice = getVoucherPrice($voucherCount);
                $استارزPrice = ceil($totalPrice / 1318);

                // Create استارز invoice
                $invoice_result = createاستارزInvoice($user_id, $voucherCount, $استارزPrice);

                if ($invoice_result && isset($invoice_result->ok) && $invoice_result->ok) {
                    $response_text = "⭐ فاکتور استارز ارسال شد!\n\n" .
                                   "✱ تعداد ووچر: $voucherCount عدد\n" .
                                   "✱ مبلغ: $استارزPrice استارز ⭐\n\n" .
                                   "✱ لطفا فاکتور ارسال شده را پرداخت کنید.\n" .
                                   "✱ پس از پرداخت، ووچرهای شما فوراً ارسال خواهد شد.";

                    // Create back button
                    $invoice_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت به منوی اصلی',
                                    'callback_data' => 'back_to_main'
                                ]
                            ]
                        ]
                    ]);
                } else {
                    $response_text = "❌ خطا در ایجاد فاکتور استارز\n\n" .
                                   "متأسفانه در حال حاضر امکان پرداخت با استارز وجود ندارد.\n" .
                                   "لطفاً از روش پرداخت کارت بانکی استفاده کنید.";

                    // Create back button
                    $invoice_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '🔙 بازگشت',
                                    'callback_data' => 'payment'
                                ]
                            ]
                        ]
                    ]);
                }

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $callback_query->message->message_id,
                    'text' => $response_text,
                    'reply_markup' => $invoice_keyboard
                ]);
                exit;
                break;
        }

        // Create back to main menu button
        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => $response_text,
            'parse_mode' => 'HTML',
            'reply_markup' => $back_keyboard
        ]);
    }

    exit;
}

// Handle new chat members (when bot is added to a group)
if (isset($update->message->new_chat_members)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $chat_type = $message->chat->type;
    $new_members = $update->message->new_chat_members;

    // Check if the bot itself was added to the group
    // Extract bot ID from API key (format: BOTID:TOKEN)
    $bot_id = explode(':', API_KEY)[0];

    foreach ($new_members as $member) {
        if ($member->id == $bot_id || ($member->is_bot && $member->username == 'BitVoucherBot')) {
            // Send welcome message when bot is added to group
            $welcome_text = "✨ صرافی بیت ووچر\n\n" .
                          "✱ جهت نصب کامل ربات را در گروه ادمین کنید.\n" .
                          "✱ با استفاده از ربات می توانید برای دیگران به صورت خودکار و چالشی کد ووچر دریافت کنید!\n" .
                          "✱ همچنین می توانید قیمت ارز های مختلف دیجیتال با ارسال اسم آن در گروه مطلع شوید.\n\n" .
                          "🤖 از اینکه ما را به گروه خود اضافه کردید متشکریم.";

            sendmessage($chat_id, $welcome_text);

            // Log the event
            if (!file_exists("logs")) {
                mkdir("logs", 0777, true);
            }
            file_put_contents('logs/group_additions.log',
                date('Y-m-d H:i:s') . " Bot added to group: $chat_id (type: $chat_type)\n",
                FILE_APPEND);

            break; // Exit loop once bot is found
        }
    }
    exit; // Exit after handling new members
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;

    // Save/update user data
    $userData = [
        'first_name' => $message->from->first_name ?? '',
        'last_name' => $message->from->last_name ?? '',
        'username' => $message->from->username ?? '',
        'last_interaction' => time(),
        'chat_id' => $chat_id
    ];

    if (!$db->exists('users', $user_id)) {
        $userData['step'] = 'start';
        $db->saveUser($user_id, $userData);
    } else {
        // Check if existing user has a token, if not generate one
        $existingUser = $db->getUser($user_id);
        if (!isset($existingUser['token']) || empty($existingUser['token'])) {
            $userData['token'] = $db->generateUserToken($user_id);
        }
        $db->update('users', $user_id, $userData);
    }

    if (strpos($text, "/start") === 0) {
        $first_name = $message->from->first_name ?? 'کاربر';

        // Check for quick invoice parameter (e.g., /start 1v, /start 5v)
        $quick_invoice_match = [];
        if (preg_match('/^\/start (\d+)v$/', $text, $quick_invoice_match)) {
            $voucher_count = intval($quick_invoice_match[1]);

            // Validate voucher count (1-50 as per documentation)
            if ($voucher_count >= 1 && $voucher_count <= 50) {
                if (checkUserMembership($user_id, $required_channels)) {
                    $db->updateUserStep($user_id, 'verified');

                    // Set voucher count in user data
                    $user = $db->getUser($user_id);
                    $userData = $user['data'] ?? [];
                    $userData['voucher_count'] = $voucher_count;
                    $db->updateUserData($user_id, $userData);

                    // Create quick invoice message
                    $totalPrice = $voucher_count * 10;
                    $finalPrice = $totalPrice + 7;
                    $استارزPrice = ceil($finalPrice * 1000 / 1318);

                    $response_text = "⚡️ فاکتور آماده\n\n" .
                                   "✱ خرید $voucher_count ووچر\n" .
                                   "✱ کارمزد : 7هزارتومان\n" .
                                   "✱ مبلغ کل : $finalPrice هزار تومان\n\n" .
                                   "💳 فاکتور شما آماده است! روش پرداخت را انتخاب کنید:";

                    // Create payment keyboard similar to lines 1291-1309
                    $payment_keyboard = json_encode([
                        'inline_keyboard' => [
                            [
                                [
                                    'text' => '⭐ پرداخت با ' . $استارزPrice . ' استارز',
                                    'callback_data' => 'pay_with_استارز'
                                ],
                                [
                                    'text' => '💳 درگاه پرداخت',
                                    'callback_data' => 'pay_with_card'
                                ]
                            ],
                            [
                                [
                                    'text' => '🔙 بازگشت',
                                    'callback_data' => 'back_to_main'
                                ]
                            ]
                        ]
                    ]);

                    // First send thank you message
                    $thank_you_text = "✨ از اینکه ما را برای پرداخت انتخاب کردید متشکریم!\n\n" .
                                    "✱ سیستم در حال پردازش فاکتور شما می باشد کمی صبر کنید...";

                    $temp_message = bot('sendMessage', [
                        'chat_id' => $chat_id,
                        'text' => $thank_you_text
                    ]);

                    // Wait 4 seconds then edit to show invoice
                    sleep(3);

                    bot('editMessageText', [
                        'chat_id' => $chat_id,
                        'message_id' => $temp_message->result->message_id,
                        'text' => $response_text,
                        'reply_markup' => $payment_keyboard
                    ]);
                    return;
                } else {
                    // User not member, show join message but remember the voucher count
                    $user = $db->getUser($user_id);
                    $userData = $user['data'] ?? [];
                    $userData['pending_voucher_count'] = $voucher_count;
                    $db->updateUserData($user_id, $userData);

                    $db->updateUserStep($user_id, 'waiting_membership');
                    $join_keyboard = createJoinKeyboard($required_channels);
                    sendmessage($chat_id,
                        "سلام $first_name 👋\n\n" .
                        "برای خرید $voucher_count ووچر، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                        $join_keyboard
                    );
                    return;
                }
            } else {
                // Invalid voucher count - show error message
                $error_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '📚 مشاهده مستندات',
                                'callback_data' => 'documentation'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت به منوی اصلی',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                sendmessage($chat_id,
                    "❌ خطا در ساخت فاکتور\n\n" .
                    "✱ علت: تعداد ووچر درخواستی ($voucher_count) خارج از محدوده مجاز است\n\n" .
                    "✱ حداقل: 1 ووچر\n" .
                    "✱ حداکثر: 50 ووچر\n\n" .
                    "لطفا تعداد ووچر را در محدوده مجاز انتخاب کنید.",
                    $error_keyboard
                );
                return;
            }
        }

        if (checkUserMembership($user_id, $required_channels)) {
            $db->updateUserStep($user_id, 'verified');

            // Check if user has pending voucher count from quick invoice
            $user = $db->getUser($user_id);
            $userData = $user['data'] ?? [];
            if (isset($userData['pending_voucher_count'])) {
                $voucher_count = $userData['pending_voucher_count'];
                unset($userData['pending_voucher_count']);
                $userData['voucher_count'] = $voucher_count;
                $db->updateUserData($user_id, $userData);

                // Show quick invoice after membership verification
                $totalPrice = $voucher_count * 10;
                $finalPrice = $totalPrice + 7;
                $استارزPrice = ceil($finalPrice * 1000 / 1318);

                $response_text = "⚡️ فاکتور آماده\n\n" .
                               "✱ خرید $voucher_count ووچر\n" .
                               "✱ کارمزد : 7هزارتومان\n" .
                               "✱ مبلغ کل : $finalPrice هزار تومان\n\n" .
                               "💳 فاکتور شما آماده است! روش پرداخت را انتخاب کنید:";

                $payment_keyboard = json_encode([
                    'inline_keyboard' => [
                        [
                            [
                                'text' => '⭐ پرداخت با ' . $استارزPrice . ' استارز',
                                'callback_data' => 'pay_with_استارز'
                            ],
                            [
                                'text' => '💳 درگاه پرداخت',
                                'callback_data' => 'pay_with_card'
                            ]
                        ],
                        [
                            [
                                'text' => '🔙 بازگشت',
                                'callback_data' => 'back_to_main'
                            ]
                        ]
                    ]
                ]);

                // First send thank you message
                $thank_you_text = "✨ از اینکه ما را برای پرداخت انتخاب کردید متشکریم!\n\n" .
                                "✱ سیستم در حال پردازش فاکتور شما می باشد کمی صبر کنید...";

                $temp_message = bot('sendMessage', [
                    'chat_id' => $chat_id,
                    'text' => $thank_you_text
                ]);

                // Wait 4 seconds then edit to show invoice
                sleep(3);

                bot('editMessageText', [
                    'chat_id' => $chat_id,
                    'message_id' => $temp_message->result->message_id,
                    'text' => $response_text,
                    'reply_markup' => $payment_keyboard
                ]);
                return;
            }

            $main_menu_text = "سلام $first_name 👋\n\n" .
                             "به ربات صرافی بیت ووچر خوش آمدید!\n\n" .
                             "با استفاده از این ربات شما می توانید پرداخت های تلگرامی خود را راحت تر انجام دهید.\n\n" .
                             "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";

            $main_menu_keyboard = createMainMenuKeyboard();
            sendmessage($chat_id, $main_menu_text, $main_menu_keyboard, 'HTML');
        } else {
            $db->updateUserStep($user_id, 'waiting_membership');
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                "سلام $first_name 👋\n\n" .
                "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                $join_keyboard
            );
        }
    }

    // Handle voucher code input for selling
    $user = $db->getUser($user_id);
    if (isset($user['step']) && $user['step'] == 'waiting_voucher_code') {
        $voucher_code = trim($text);

        // Validate voucher code format (BV-XXXX-XXXX-XXXX-XXXX)
        if (!preg_match('/^BV-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/', $voucher_code)) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ فرمت کد ووچر نادرست است!\n\n" .
                "لطفا کد ووچر را با فرمت صحیح ارسال کنید:\n" .
                "مثال: BV-XXXX-XXXX-XXXX-XXXX",
                $error_keyboard
            );
            return;
        }

        // Check if voucher exists
        $voucherData = findVoucherByCode($voucher_code);
        if (!$voucherData) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ کد ووچر وارد شده یافت نشد!\n\n" .
                "لطفا کد ووچر را بررسی کرده و مجدداً ارسال کنید.",
                $error_keyboard
            );
            return;
        }

        // Check if voucher is already used
        if ($voucherData['is_used']) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ این ووچر قبلاً استفاده شده است!\n\n" .
                "✱ تاریخ استفاده: " . $voucherData['used_at'],
                $error_keyboard
            );
            return;
        }

        // Allow anyone to sell any valid voucher (ownership check removed)

        // Process voucher sale
        $voucher_amount = $voucherData['amount_toman'];

        // Update user balance using helper method
        $current_balance = $db->getUserBalance($user_id);
        $db->addUserBalance($user_id, $voucher_amount);
        $new_balance = $current_balance + $voucher_amount;

        // Mark voucher as used
        $voucherData['is_used'] = true;
        $voucherData['used_by'] = $user_id;
        $voucherData['used_at'] = date('Y-m-d H:i:s');
        $voucherData['status'] = 'sold';

        $voucherFile = "data/vouchers/{$voucher_code}.json";
        file_put_contents($voucherFile, json_encode($voucherData, JSON_PRETTY_PRINT));

        // Save transaction record
        $transactionId = 'TXN-' . time() . '-' . $user_id;
        $db->saveTransaction($transactionId, [
            'user_id' => $user_id,
            'voucher_code' => $voucher_code,
            'amount' => $voucher_amount,
            'currency' => 'IRR',
            'type' => 'voucher_sale',
            'status' => 'completed',
            'created_at' => time()
        ]);

        // Reset user step
        $db->updateUserStep($user_id, 'verified');

        // Send success message with back button
        $success_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        sendmessage($chat_id,
            "✅ ووچر با موفقیت فروخته شد!\n\n" .
            "✱ مبلغ: " . number_format($voucher_amount) . " تومان\n" .
            "✱ موجودی جدید: " . number_format($new_balance) . " تومان\n" .
            "✱ کد ووچر: <code>$voucher_code</code>\n\n" .
            "✱ مبلغ به کیف پول شما اضافه شد.",
            $success_keyboard,
            'HTML'
        );
    }

    // Handle card number input for withdrawal
    if (isset($user['step']) && $user['step'] == 'waiting_card_number') {
        $card_input = trim($text);

        // Validate card number format (xxxx-xxxx-xxxx-xxxx : Name)
        if (!preg_match('/^\d{4}-\d{4}-\d{4}-\d{4}\s*:\s*.+$/', $card_input)) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به کیف پول',
                            'callback_data' => 'wallet'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ فرمت شماره کارت نادرست است!\n\n" .
                "لطفا شماره کارت را با فرمت صحیح ارسال کنید:\n" .
                "مثال: 1234-5678-9012-3456 : علی حسینی\n\n" .
                "✱ شماره کارت - فاصله - دونقطه - فاصله - نام صاحب کارت",
                $error_keyboard
            );
            return;
        }

        // Save card info and move to next step
        $userData = $user['data'] ?? [];
        $userData['withdrawal_card'] = $card_input;
        $db->updateUserData($user_id, $userData);
        $db->updateUserStep($user_id, 'waiting_withdrawal_amount');

        $balance = $db->getUserBalance($user_id);
        $response_text = "💸 برداشت موجودی\n\n" .
                       "✱ شماره کارت ثبت شد\n" .
                       "✱ کارت: $card_input\n" .
                       "✱ موجودی شما: " . number_format($balance) . " تومان\n\n" .
                       "💰 لطفا مبلغی که می‌خواهید برداشت کنید را به تومان وارد کنید:\n\n" .
                       "✱ حداقل مبلغ برداشت: 30,000 تومان\n" .
                       "✱ حداکثر مبلغ برداشت: " . number_format($balance) . " تومان";

        $back_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به کیف پول',
                        'callback_data' => 'wallet'
                    ]
                ]
            ]
        ]);

        sendmessage($chat_id, $response_text, $back_keyboard, 'HTML');
        return;
    }

    // Handle withdrawal amount input
    if (isset($user['step']) && $user['step'] == 'waiting_withdrawal_amount') {
        $amount_input = trim($text);

        // Validate amount is numeric
        if (!is_numeric($amount_input) || $amount_input <= 0) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به کیف پول',
                            'callback_data' => 'wallet'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ مبلغ وارد شده نادرست است!\n\n" .
                "لطفا مبلغ را به صورت عدد وارد کنید.\n" .
                "مثال: 50000",
                $error_keyboard
            );
            return;
        }

        $withdrawal_amount = intval($amount_input);
        $balance = $db->getUserBalance($user_id);

        // Check minimum amount
        if ($withdrawal_amount < 30000) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به کیف پول',
                            'callback_data' => 'wallet'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ مبلغ برداشت کمتر از حد مجاز است!\n\n" .
                "✱ حداقل مبلغ برداشت: 30,000 تومان\n" .
                "✱ مبلغ وارد شده: " . number_format($withdrawal_amount) . " تومان",
                $error_keyboard
            );
            return;
        }

        // Check if user has enough balance
        if ($withdrawal_amount > $balance) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به کیف پول',
                            'callback_data' => 'wallet'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ موجودی کافی نیست!\n\n" .
                "✱ موجودی شما: " . number_format($balance) . " تومان\n" .
                "✱ مبلغ درخواستی: " . number_format($withdrawal_amount) . " تومان",
                $error_keyboard
            );
            return;
        }

        // Process withdrawal
        $userData = $user['data'] ?? [];
        $card_info = $userData['withdrawal_card'] ?? 'نامشخص';

        // Subtract balance immediately when withdrawal is successful
        if ($db->subtractUserBalance($user_id, $withdrawal_amount)) {
            $new_balance = $balance - $withdrawal_amount;

            // Save withdrawal transaction with completed status
            $transactionId = 'WTH-' . time() . '-' . $user_id;
            $db->saveTransaction($transactionId, [
                'user_id' => $user_id,
                'amount' => $withdrawal_amount,
                'currency' => 'IRR',
                'type' => 'withdrawal',
                'status' => 'completed',  // تغییر از pending به completed
                'card_info' => $card_info,
                'created_at' => time()
            ]);

            // Reset user step
            $db->updateUserStep($user_id, 'verified');

            // Clear withdrawal card data (get fresh user data to avoid overwriting balance)
            $freshUser = $db->getUser($user_id);
            $freshUserData = $freshUser['data'] ?? [];
            unset($freshUserData['withdrawal_card']);
            $db->updateUserData($user_id, $freshUserData);

            $success_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "✅ برداشت موجودی با موفقیت انجام شد!\n\n" .
                "✱ کارت: $card_info\n" .
                "✱ مبلغ برداشت: " . number_format($withdrawal_amount) . " تومان\n" .
                "✱ موجودی جدید: " . number_format($new_balance) . " تومان\n" .
                "✱ مبلغ از کیف پول شما کسر شد.\n" .
                "✱ کد پیگیری: <code>$transactionId</code>",
                $success_keyboard,
                'HTML'
            );
        } else {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به کیف پول',
                            'callback_data' => 'wallet'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ خطا در پردازش برداشت!\n\n" .
                "لطفا مجدداً تلاش کنید.",
                $error_keyboard
            );
        }
        return;
    }

    // Handle support message input
    if (isset($user['step']) && $user['step'] == 'waiting_support_message') {
        $support_message = trim($text);

        // Validate message is not empty
        if (empty($support_message)) {
            $error_keyboard = json_encode([
                'inline_keyboard' => [
                    [
                        [
                            'text' => '🔙 بازگشت به منوی اصلی',
                            'callback_data' => 'back_to_main'
                        ]
                    ]
                ]
            ]);

            sendmessage($chat_id,
                "❌ پیام نمی‌تواند خالی باشد!\n\n" .
                "لطفا پیام خود را برای تیم پشتیبانی ارسال کنید.",
                $error_keyboard
            );
            return;
        }

        // Generate support ticket ID
        $ticketId = 'SUP-' . time() . '-' . $user_id;

        // Get user info
        $first_name = $user['first_name'] ?? 'کاربر';
        $last_name = $user['last_name'] ?? '';
        $username = $user['username'] ?? 'بدون نام کاربری';
        $full_name = trim($first_name . ' ' . $last_name);

        // Save support ticket
        $ticketData = [
            'user_id' => $user_id,
            'user_name' => $full_name,
            'username' => $username,
            'message' => $support_message,
            'status' => 'pending',
            'created_at' => time(),
            'created_date' => date('Y-m-d H:i:s')
        ];

        $db->save('support_tickets', $ticketId, $ticketData);

        // Reset user step
        $db->updateUserStep($user_id, 'verified');

        // Send success message
        $success_keyboard = json_encode([
            'inline_keyboard' => [
                [
                    [
                        'text' => '🔙 بازگشت به منوی اصلی',
                        'callback_data' => 'back_to_main'
                    ]
                ]
            ]
        ]);

        sendmessage($chat_id,
            "✅ پیام شما به پشتیبانی ارسال شد!\n\n" .
            "✱ کد پیگیری: <code>$ticketId</code>\n" .
            "✱ تاریخ ارسال: " . date('Y/m/d H:i') . "\n\n" .
            "✱ تیم پشتیبانی در اسرع وقت پاسخ شما را خواهد داد.\n" .
            "✱ لطفا کد پیگیری را نزد خود نگه دارید.",
            $success_keyboard,
            'HTML'
        );
        return;
    }

    // Handle cryptocurrency price requests in groups
    if ($message->chat->type == 'group' || $message->chat->type == 'supergroup') {
        $crypto_keywords = [
            'بیت کوین' => 'BTC', 'Bitcoin' => 'BTC', 'BTC' => 'BTC',
            'اتریوم' => 'ETH', 'Ethereum' => 'ETH', 'ETH' => 'ETH',
            'بیت کوین کش' => 'BCH', 'Bitcoin Cash' => 'BCH', 'BCH' => 'BCH',
            'لایت کوین' => 'LTC', 'LiteCoin' => 'LTC', 'LTC' => 'LTC',
            'دش' => 'DASH', 'Dash' => 'DASH', 'DASH' => 'DASH',
            'دوج کوین' => 'DOGE', 'Dogecoin' => 'DOGE', 'DOGE' => 'DOGE',
            'تتر' => 'USDT', 'Tether' => 'USDT', 'USDT' => 'USDT',
            'ریپل' => 'XRP', 'Ripple' => 'XRP', 'XRP' => 'XRP',
            'ترون' => 'TRX', 'Tron' => 'TRX', 'TRX' => 'TRX',
            'استلار' => 'XLM', 'Stellar' => 'XLM', 'XLM' => 'XLM',
            'ایاس' => 'EOS', 'EOS' => 'EOS',
            'کاردانو' => 'ADA', 'Cardano' => 'ADA', 'ADA' => 'ADA',
            'بایننس کوین' => 'BNB', 'Binance Coin' => 'BNB', 'BNB' => 'BNB',
            'اتم' => 'ATOM', 'Cosmos' => 'ATOM', 'ATOM' => 'ATOM',
            'پالیگان' => 'MATIC', 'Polygon' => 'MATIC', 'MATIC' => 'MATIC',
            'فانتوم' => 'FTM', 'Fantom' => 'FTM', 'FTM' => 'FTM',
            'پولکادات' => 'DOT', 'Polkadot' => 'DOT', 'DOT' => 'DOT',
            'شیبا اینو' => 'SHIB', 'Shiba Inu' => 'SHIB', 'SHIB' => 'SHIB',
            'فایل کوین' => 'FIL', 'Filecoin' => 'FIL', 'FIL' => 'FIL',
            'پنکیک سواپ' => 'CAKE', 'PancakeSwap' => 'CAKE', 'CAKE' => 'CAKE',
            'چین لینک' => 'LINK', 'Chainlink' => 'LINK', 'LINK' => 'LINK',
            'یونی سواپ' => 'UNI', 'Uniswap' => 'UNI', 'UNI' => 'UNI',
            'تورچین' => 'RUNE', 'Thorchain' => 'RUNE', 'RUNE' => 'RUNE',
            'میجر' => 'MAJOR', 'Major' => 'MAJOR', 'MAJOR' => 'MAJOR',
            'گوتز' => 'GOATS', 'GOATS' => 'GOATS',
            'سی' => 'SEI', 'Sei' => 'SEI', 'SEI' => 'SEI',
            'اسموسیس' => 'OSMO', 'Osmosis' => 'OSMO', 'OSMO' => 'OSMO',
            'سونیک' => 'S', 'Sonic' => 'S', 'S' => 'S',
            'داگ ویف هت' => 'WIF', 'dogwifhat' => 'WIF', 'WIF' => 'WIF',
            'پایت نتورک' => 'PYTH', 'Pyth Network' => 'PYTH', 'PYTH' => 'PYTH',
            'ورم هول' => 'W', 'Wormhole' => 'W', 'W' => 'W',
            'ریدیوم' => 'RAY', 'Raydium' => 'RAY', 'RAY' => 'RAY',
            'بونک' => 'BONK', 'Bonk' => 'BONK', 'BONK' => 'BONK',
            'تپ سواپ' => 'TAPS', 'TapSwap' => 'TAPS', 'TAPS' => 'TAPS',
            'ژوپیتر' => 'JUP', 'Jupiter' => 'JUP', 'JUP' => 'JUP',
            'جیتو' => 'JTO', 'Jito' => 'JTO', 'JTO' => 'JTO',
            'پینات' => 'PNUT', 'Peanut the Squirrel' => 'PNUT', 'PNUT' => 'PNUT',
            'ساموید کوین' => 'SAMO', 'Samoyedcoin' => 'SAMO', 'SAMO' => 'SAMO',
            'نکسو' => 'NEXO', 'Nexo' => 'NEXO', 'NEXO' => 'NEXO',
            'پاجی پنگوئن' => 'PENGU', 'Pudgy Penguins' => 'PENGU', 'PENGU' => 'PENGU',
            'مجیک ادن' => 'ME', 'Magic Eden' => 'ME', 'ME' => 'ME',
            'دیسنترالند' => 'MANA', 'Decentraland' => 'MANA', 'MANA' => 'MANA',
            'اکسی' => 'AXS', 'Axie Infinity' => 'AXS', 'AXS' => 'AXS',
            'سندباکس' => 'SAND', 'Sandbox' => 'SAND', 'SAND' => 'SAND',
            'انجین کوین' => 'ENJ', 'Enjin Coin' => 'ENJ', 'ENJ' => 'ENJ',
            'آلیس' => 'ALICE', 'MyNeighborAlice' => 'ALICE', 'ALICE' => 'ALICE',
            'چیلیز' => 'CHZ', 'Chiliz' => 'CHZ', 'CHZ' => 'CHZ',
            'بیت تورنت' => 'BTT', 'BitTorrent' => 'BTT', 'BTT' => 'BTT',
            'داگز' => 'DOGS', 'DOGS' => 'DOGS',
            'دلار بایننس' => 'BUSD', 'Binance USD' => 'BUSD', 'BUSD' => 'BUSD'
        ];

        // Check if message contains any cryptocurrency keyword with optional amount
        foreach ($crypto_keywords as $keyword => $symbol) {
            if (stripos($text, $keyword) !== false) {
                // Check if there's a number before the cryptocurrency name
                $amount = 1; // Default amount
                $pattern = '/(\d+(?:\.\d+)?)\s*' . preg_quote($keyword, '/') . '/i';
                if (preg_match($pattern, $text, $matches)) {
                    $amount = floatval($matches[1]);
                }

                $crypto_data = getCryptocurrencyPrice($symbol);
                if ($crypto_data) {
                    sendCryptoPriceMessage($chat_id, $crypto_data, $message->message_id, $amount);
                }
                break; // Only respond to the first match
            }
        }
    }
}

// Function to fetch cryptocurrency price from API
function getCryptocurrencyPrice($symbol) {
    $api_url = "https://one-api.ir/DigitalCurrency/?token=189946:6877816e1d4ba";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($http_code == 200 && $response) {
        $data = json_decode($response, true);

        if (isset($data['result']) && is_array($data['result'])) {
            // Search for the cryptocurrency by symbol
            foreach ($data['result'] as $crypto) {
                if (isset($crypto['key']) && strtoupper($crypto['key']) == strtoupper($symbol)) {
                    return $crypto;
                }
            }
        }
    }

    return null;
}

// Function to send cryptocurrency price message
function sendCryptoPriceMessage($chat_id, $crypto_data, $reply_to_message_id = null, $amount = 1) {
    $name = $crypto_data['name'] ?? 'نامشخص';
    $name_en = $crypto_data['name_en'] ?? 'Unknown';
    $symbol = $crypto_data['key'] ?? '';
    $price = $crypto_data['price'] ?? 0;
    $change_24h = $crypto_data['percent_change_24h'] ?? 0;
    $change_7d = $crypto_data['percent_change_7d'] ?? 0;
    $market_cap = $crypto_data['market_cap'] ?? 0;
    $volume_24h = $crypto_data['volume_24h'] ?? 0;
    $rank = $crypto_data['rank'] ?? 0;

    // USD to Toman exchange rate
    $usd_to_toman = 87000;

    // Format numbers
    $formatted_price_usd = number_format($price, 6);
    $price_toman = $price * $usd_to_toman;
    $formatted_price_toman = number_format($price_toman, 0);

    // Calculate total value if amount is specified
    $total_value_usd = $price * $amount;
    $total_value_toman = $total_value_usd * $usd_to_toman;
    $formatted_total_value_usd = number_format($total_value_usd, 2);
    $formatted_total_value_toman = number_format($total_value_toman, 0);

    $message = "🪙 قیمت ارز دیجیتال\n\n" .
               "✱ نام: $name ($name_en)\n" .
               "✱ نماد: $symbol\n" .
               "✱ قیمت: $" . $formatted_price_usd . " | " . $formatted_price_toman . " تومان\n" .
               "✱ رتبه: #$rank\n\n";

    // Add amount calculation if amount is greater than 1
    if ($amount > 1) {
        $formatted_amount = number_format($amount, ($amount == intval($amount)) ? 0 : 2);
        $message .= "<blockquote>👀 محاسبه مقدار وارد شده:\n" .
                   "✱ $formatted_amount $symbol = $" . $formatted_total_value_usd . " | " . $formatted_total_value_toman . " تومان</blockquote>";
    }

    // Create inline keyboard with "Add to Group" button
    $keyboard = json_encode([
        'inline_keyboard' => [
            [
                [
                    'text' => '➕ افزودن به گروه',
                    'url' => 'https://t.me/BitVoucherBot?startgroup=true'
                ]
            ]
        ]
    ]);

    // Send message with reply and inline keyboard
    $data = [
        'chat_id' => $chat_id,
        'text' => $message,
        'reply_markup' => $keyboard,
        'parse_mode' => 'HTML'
    ];

    // Add reply_to_message_id if provided
    if ($reply_to_message_id) {
        $data['reply_to_message_id'] = $reply_to_message_id;
    }

    bot('sendMessage', $data);
}

// Handle pre-checkout query for استارز payments
if (isset($update->pre_checkout_query)) {
    $pre_checkout_query = $update->pre_checkout_query;
    $query_id = $pre_checkout_query->id;
    $order_id = $pre_checkout_query->invoice_payload;

    // Log pre-checkout query
    if (!file_exists("logs")) {
        mkdir("logs", 0777, true);
    }
    file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " Pre-checkout query received: query_id=$query_id, order_id=$order_id\n", FILE_APPEND);

    // Verify order exists
    $orderData = $db->load('orders', $order_id);

    if ($orderData && $orderData['status'] == 'pending') {
        // Answer pre-checkout query with OK
        bot('answerPreCheckoutQuery', [
            'pre_checkout_query_id' => $query_id,
            'ok' => true
        ]);

        file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " Pre-checkout approved for order: $order_id\n", FILE_APPEND);
    } else {
        // Answer pre-checkout query with error
        bot('answerPreCheckoutQuery', [
            'pre_checkout_query_id' => $query_id,
            'ok' => false,
            'error_message' => 'سفارش نامعتبر یا منقضی شده است.'
        ]);

        file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " Pre-checkout rejected for order: $order_id\n", FILE_APPEND);
    }
    exit;
}

// Handle successful payment for استارز
if (isset($update->message->successful_payment)) {
    $successful_payment = $update->message->successful_payment;
    $order_id = $successful_payment->invoice_payload;
    $user_id = $update->message->from->id;
    $chat_id = $update->message->chat->id;

    // Log successful payment
    file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " Successful استارز payment: order_id=$order_id, user_id=$user_id\n", FILE_APPEND);

    // Get order data
    $orderData = $db->load('orders', $order_id);

    if ($orderData && $orderData['status'] == 'pending') {
        $voucherCount = $orderData['voucher_count'];
        $استارزAmount = $orderData['استارز_amount'];

        // Generate single voucher with total value
        $voucherCode = generateVoucherCode();
        $totalVoucherValue = $voucherCount * 10000; // Total value without commission

        $voucherData = [
            'voucher_code' => $voucherCode,
            'creator_user_id' => $user_id,
            'creator_token' => 'استارز',
            'amount_toman' => $totalVoucherValue,
            'track_id' => $order_id,
            'created_at' => date('Y-m-d H:i:s'),
            'created_timestamp' => time(),
            'is_used' => false,
            'used_by' => null,
            'used_at' => null,
            'status' => 'active',
            'payment_method' => 'استارز',
            'order_id' => $order_id
        ];

        // Save voucher to old format (for compatibility)
        $voucherFile = "data/vouchers/{$voucherCode}.json";
        if (!file_exists("data/vouchers")) {
            mkdir("data/vouchers", 0777, true);
        }
        file_put_contents($voucherFile, json_encode($voucherData, JSON_PRETTY_PRINT));

        // Save transaction record
        $transactionId = 'TXN-' . time() . '-' . $user_id;
        $db->saveTransaction($transactionId, [
            'user_id' => $user_id,
            'voucher_code' => $voucherCode,
            'amount' => $totalVoucherValue,
            'currency' => 'IRR',
            'type' => 'voucher_purchase',
            'payment_method' => 'استارز',
            'status' => 'completed',
            'created_at' => time()
        ]);

        // Update order status
        $orderData['status'] = 'completed';
        $orderData['completed_at'] = time();
        $orderData['voucher_code'] = $voucherCode;
        $db->save('orders', $order_id, $orderData);

        // Send success message (single voucher with total value)
        $payment_time = date('Y/m/d H:i:s');
        $totalPaidAmount = $استارزAmount * 1318; // Convert Stars back to Toman for display

        $success_message = "✅ پرداخت موفق!\n\n" .
                          "✱ زمان : $payment_time\n" .
                          "✱ مبلغ : " . number_format($totalPaidAmount) . " تومان\n" .
                          "✱ شناسه پیگیری : <code>$order_id</code>\n\n" .
                          "🎟 کد ووچر شما : <code>$voucherCode</code>\n" .
                          "✱ ارزش ووچر : " . number_format($totalVoucherValue) . " تومان\n" .
                          "✱ کد دریافتی را در جای امنی ذخیره کنید.\n" .
                          "✱ باتشکر از پرداخت شما.";

        sendmessage($chat_id, $success_message, null, 'HTML');

        file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " Vouchers generated and sent for order: $order_id\n", FILE_APPEND);
    } else {
        sendmessage($chat_id, "❌ خطا در پردازش پرداخت! لطفا با پشتیبانی تماس بگیرید.");
        file_put_contents('logs/استارز_payment.log', date('Y-m-d H:i:s') . " ERROR: Order not found or already processed: $order_id\n", FILE_APPEND);
    }
    exit;
}

// Function to generate voucher code
function generateVoucherCode() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = 'BV-';

    for ($i = 0; $i < 4; $i++) {
        for ($j = 0; $j < 4; $j++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        if ($i < 3) {
            $code .= '-';
        }
    }

    return $code;
}

?>