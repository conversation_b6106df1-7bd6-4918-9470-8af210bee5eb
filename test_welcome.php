<?php

// Test script to simulate bot being added to a group
require_once 'config.php';

// Simulate a webhook update where bot is added to a group
$test_update = [
    'message' => [
        'message_id' => 123,
        'from' => [
            'id' => 123456789,
            'is_bot' => false,
            'first_name' => 'Test User'
        ],
        'chat' => [
            'id' => -1001234567890,
            'type' => 'supergroup',
            'title' => 'Test Group'
        ],
        'date' => time(),
        'new_chat_members' => [
            [
                'id' => 7626326794, // Bot ID from API key
                'is_bot' => true,
                'first_name' => 'BitVoucher Bot',
                'username' => 'BitVoucherBot'
            ]
        ]
    ]
];

// Convert to JSON and simulate webhook input
$json_update = json_encode($test_update);
echo "Test update JSON:\n";
echo $json_update . "\n\n";

// Test the bot ID extraction
$bot_id = explode(':', $API_KEY)[0];
echo "Extracted Bot ID: " . $bot_id . "\n";
echo "Expected Bot ID: 7626326794\n";
echo "Match: " . ($bot_id == '7626326794' ? 'Yes' : 'No') . "\n\n";

echo "Test completed. The welcome message should be sent when this update is processed by bot.php\n";

?>
